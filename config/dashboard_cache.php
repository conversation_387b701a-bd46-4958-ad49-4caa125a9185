<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Dashboard Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Configure caching durations for different dashboard components
    |
    */

    'cache_durations' => [
        // Sales data - updates frequently
        'sales_data' => 300, // 5 minutes
        
        // Statistics - moderate updates
        'statistics' => 300, // 5 minutes
        
        // Graph data - less frequent updates
        'graph_data' => 300, // 5 minutes
        
        // Heatmap data - can be cached longer
        'heatmap' => 600, // 10 minutes
        
        // PBT data - rarely changes
        'pbt_data' => 3600, // 1 hour
        
        // Company sales - moderate updates
        'company_sales' => 300, // 5 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefixes
    |--------------------------------------------------------------------------
    */
    'cache_prefixes' => [
        'sales' => 'dashboard_sales_',
        'statistics' => 'dashboard_stats_',
        'graph' => 'dashboard_graph_',
        'heatmap' => 'dashboard_heatmap_',
        'company' => 'dashboard_company_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Query Optimization Settings
    |--------------------------------------------------------------------------
    */
    'query_limits' => [
        'heatmap_points' => 1000,
        'latest_orders' => 5,
        'top_products' => 10,
        'pagination_size' => 10,
    ],
];