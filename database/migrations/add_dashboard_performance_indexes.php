<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations for dashboard performance optimization
     */
    public function up()
    {
        // Orders table indexes
        Schema::table('orders', function (Blueprint $table) {
            // For dataSales function
            $table->index(['user_id', 'order_date', 'deleted_at'], 'idx_orders_user_date_deleted');
            
            // For statisticActive function
            $table->index(['company_id', 'order_date'], 'idx_orders_company_date');
            
            // For heatmap queries
            $table->index(['order_latitude', 'order_longitude', 'order_date'], 'idx_orders_location_date');
            
            // For payment method analysis
            $table->index(['pay_method', 'order_date', 'deleted_at'], 'idx_orders_payment_date');
        });

        // Companies table indexes
        Schema::table('companies', function (Blueprint $table) {
            // For user-company relationships
            $table->index(['user_id', 'created_at'], 'idx_companies_user_created');
            
            // For PBT relationships
            $table->index(['pbt_id', 'category_id'], 'idx_companies_pbt_category');
        });

        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            // For access module filtering
            $table->index(['access_module', 'username'], 'idx_users_access_username');
        });

        // PBTs table indexes
        Schema::table('pbts', function (Blueprint $table) {
            // For state and status filtering
            $table->index(['state', 'status'], 'idx_pbts_state_status');
        });

        // Products table indexes
        Schema::table('products', function (Blueprint $table) {
            // For stock analysis
            $table->index(['product_stock', 'name'], 'idx_products_stock_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_user_date_deleted');
            $table->dropIndex('idx_orders_company_date');
            $table->dropIndex('idx_orders_location_date');
            $table->dropIndex('idx_orders_payment_date');
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->dropIndex('idx_companies_user_created');
            $table->dropIndex('idx_companies_pbt_category');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_access_username');
        });

        Schema::table('pbts', function (Blueprint $table) {
            $table->dropIndex('idx_pbts_state_status');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_stock_name');
        });
    }
};